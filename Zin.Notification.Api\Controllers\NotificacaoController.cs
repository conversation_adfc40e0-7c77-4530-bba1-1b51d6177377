using Microsoft.AspNetCore.Mvc;
using Zin.Notification.Application.Dtos;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Domain.Entities;

namespace Zin.Notification.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NotificacaoController : ControllerBase
{
    private readonly INotificacaoOrchestrator _notificacaoOrchestrator;

    public NotificacaoController(INotificacaoOrchestrator notificacaoOrchestrator)
    {
        _notificacaoOrchestrator = notificacaoOrchestrator;
    }

    [HttpPost]
    [ProducesResponseType(typeof(object), StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> EnfileirarNotificacao([FromBody] NotificacaoRequest? request)
    {
        if (request is null)
            return BadRequest("Requisição inválida.");

        var id = await _notificacaoOrchestrator.CriarEEnfileirarAsync(request);
        return Accepted(new { id });
    }

    [HttpPost("{id}/reprocessar")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ReprocessarNotificacao(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            return BadRequest("ID da notificação é obrigatório.");

        await _notificacaoOrchestrator.ReprocessarAsync(id);
        return Accepted();
    }

    [HttpGet("falhas")]
    [ProducesResponseType(typeof(IEnumerable<Notificacao>), StatusCodes.Status200OK)]
    public async Task<IActionResult> ObterNotificacoesFalhas()
    {
        var falhas = await _notificacaoOrchestrator.ObterFalhasAsync();
        return Ok(falhas);
    }

    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Notificacao), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ObterNotificacao(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            return BadRequest("ID da notificação é obrigatório.");

        var notificacao = await _notificacaoOrchestrator.ObterPorIdAsync(id);
        if (notificacao is null)
            return NotFound($"Notificação com ID '{id}' não encontrada.");

        return Ok(notificacao);
    }
}
