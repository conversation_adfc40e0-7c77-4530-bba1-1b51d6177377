﻿using MailKit.Net.Smtp;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MimeKit;
using Polly;
using Polly.Retry;
using Zin.Notification.Application.Configs;
using Zin.Notification.Application.Interfaces;

namespace Zin.Notification.Infrastructure.ExternalClients;

public class EmailSender : IEmailSender
{
    private readonly EmailSettings _settings;
    private readonly ILogger<EmailSender> _logger;
    private readonly ResiliencePipeline _resilience;

    public EmailSender(IOptions<EmailSettings> settings, ILogger<EmailSender> logger)
    {
        _settings = settings.Value;
        _logger = logger;

        _resilience = new ResiliencePipelineBuilder()
            .AddRetry(new RetryStrategyOptions
            {
                ShouldHandle = new PredicateBuilder().Handle<Exception>(),
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromSeconds(1),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                OnRetry = args =>
                {
                    _logger.LogWarning("Tentativa {AttemptNumber} de envio de e-mail falhou. Erro: {Exception}",
                        args.AttemptNumber, args.Outcome.Exception?.Message);
                    return ValueTask.CompletedTask;
                }
            })
            .AddTimeout(TimeSpan.FromSeconds(30))
            .Build();
    }

    public async Task<bool> EnviarAsync(string destinatario, string mensagem, string assunto)
    {
        var result = await _resilience.ExecuteAsync(async _ =>
        {
            var email = new MimeMessage();
            email.From.Add(new MailboxAddress("Sistema ZIN", _settings.From));
            email.To.Add(new MailboxAddress("Destinatário", destinatario));
            email.Subject = assunto;
            email.Body = new TextPart("html") { Text = mensagem };

            using var client = new SmtpClient();
            await client.ConnectAsync(_settings.Host, _settings.Port, MailKit.Security.SecureSocketOptions.StartTls);
            await client.AuthenticateAsync(_settings.User, _settings.Password);
            await client.SendAsync(email);
            await client.DisconnectAsync(true);

            return true;
        });

        if (result)
            _logger.LogInformation("E-mail enviado com sucesso para {Destinatario}", destinatario);

        return result;
    }
}