﻿using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Domain.Entities;

public interface INotificacaoRepository
{
    Task<string> CriarAsync(Notificacao notificacao);
    Task<Notificacao?> ObterPorIdAsync(string id);
    Task AtualizarAsync(Notificacao notificacao);
    Task<IEnumerable<Notificacao>> ObterPorStatusAsync(NotificacaoStatus status);
    Task<IEnumerable<Notificacao>> ObterFalhasParaReprocessamentoAsync();
    Task<IEnumerable<Notificacao>> ObterPendentesAsync();
}
