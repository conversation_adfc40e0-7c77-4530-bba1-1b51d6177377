using Moq;
using Xunit;
using Microsoft.Extensions.Logging;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Application.Services;
using Zin.Notification.Domain.Entities;
using Zin.Notification.Domain.Enums;
using Zin.Notification.Application.Tests.Fakes;

namespace Zin.Notification.Application.Tests;

public class NotificacaoSenderTests
{
    private readonly Mock<INotificacaoRepository> _mockRepository;
    private readonly Mock<ILogger<NotificacaoDispatcher>> _mockLogger;
    private readonly Mock<ICanalResolver> _mockResolver;
    private readonly Mock<INotificacaoStrategy> _mockStrategy;
    private readonly INotificacaoDispatcher _sender;
    private readonly List<Notificacao> _updatedNotifications;

    public NotificacaoSenderTests()
    {
        _mockRepository = new Mock<INotificacaoRepository>();
        _mockLogger = new Mock<ILogger<NotificacaoDispatcher>>();
        _mockResolver = new Mock<ICanalResolver>();
        _mockStrategy = new Mock<INotificacaoStrategy>();
        _updatedNotifications = new List<Notificacao>();

        _mockRepository.Setup(r => r.AtualizarAsync(It.IsAny<Notificacao>()))
            .Callback<Notificacao>(n =>
            {
                var clone = NotificacaoFakes.Clone(n);
                _updatedNotifications.Add(clone);
            })
            .Returns(Task.CompletedTask);

        _mockResolver.Setup(r => r.Resolver(It.IsAny<NotificacaoCanal>()))
            .Returns((NotificacaoCanal canal) =>
            {
                return canal == NotificacaoCanal.Email ? _mockStrategy.Object : null;
            });

        _sender = new NotificacaoDispatcher(
            _mockRepository.Object,
            _mockLogger.Object,
            _mockResolver.Object
        );
    }

    [Fact]
    public async Task EnviarNotificacaoAsync_DeveChamarStrategyEAtualizarStatus_QuandoCanalForSuportado()
    {
        // Arrange
        var notificacao = NotificacaoFakes.CriarNotificacao(NotificacaoCanal.Email);
        _mockRepository.Setup(r => r.ObterPorIdAsync(notificacao.Id)).ReturnsAsync(notificacao);

        // Act
        await _sender.EnviarNotificacaoAsync(notificacao.Id);

        // Assert
        _mockStrategy.Verify(s => s.EnviarAsync(notificacao.Destinatario, notificacao.Mensagem, It.IsAny<string>()), Times.Once);
        _mockRepository.Verify(r => r.AtualizarAsync(It.IsAny<Notificacao>()), Times.Exactly(2));

        Assert.Equal(NotificacaoStatus.Processando, _updatedNotifications[0].Status);
        Assert.Equal(NotificacaoStatus.Enviada, _updatedNotifications[1].Status);
    }

    [Fact]
    public async Task EnviarNotificacaoAsync_DeveFalhar_QuandoCanalNaoForSuportado()
    {
        // Arrange
        var notificacao = NotificacaoFakes.CriarNotificacao((NotificacaoCanal)99);
        _mockRepository.Setup(r => r.ObterPorIdAsync(notificacao.Id)).ReturnsAsync(notificacao);

        // Act & Assert
        await Assert.ThrowsAsync<NotSupportedException>(() => _sender.EnviarNotificacaoAsync(notificacao.Id));

        _mockRepository.Verify(r => r.AtualizarAsync(It.IsAny<Notificacao>()), Times.Exactly(2));

        Assert.Equal(NotificacaoStatus.Processando, _updatedNotifications[0].Status);
        Assert.Equal(NotificacaoStatus.Falha, _updatedNotifications[1].Status);
    }

    [Fact]
    public async Task EnviarNotificacaoAsync_DeveFalhar_QuandoStrategyLancarExcecao()
    {
        // Arrange
        var notificacao = NotificacaoFakes.CriarNotificacao(NotificacaoCanal.Email);
        _mockRepository.Setup(r => r.ObterPorIdAsync(notificacao.Id)).ReturnsAsync(notificacao);
        _mockStrategy.Setup(s => s.EnviarAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new Exception("Erro de envio"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _sender.EnviarNotificacaoAsync(notificacao.Id));

        _mockRepository.Verify(r => r.AtualizarAsync(It.IsAny<Notificacao>()), Times.Exactly(2));

        Assert.Equal(NotificacaoStatus.Processando, _updatedNotifications[0].Status);
        Assert.Equal(NotificacaoStatus.Falha, _updatedNotifications[1].Status);
    }

    [Fact]
    public async Task EnviarNotificacaoAsync_DeveLancarExcecao_QuandoNotificacaoNaoForEncontrada()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        _mockRepository.Setup(r => r.ObterPorIdAsync(id)).ReturnsAsync((Notificacao?)null);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _sender.EnviarNotificacaoAsync(id));

        _mockRepository.Verify(r => r.AtualizarAsync(It.IsAny<Notificacao>()), Times.Never);
        _mockStrategy.Verify(s => s.EnviarAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }
}