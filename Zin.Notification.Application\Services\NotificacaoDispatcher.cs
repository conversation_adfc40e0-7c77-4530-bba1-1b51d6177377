using Microsoft.Extensions.Logging;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Domain.Entities;
using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Application.Services;

public class NotificacaoDispatcher : INotificacaoDispatcher
{
    private readonly INotificacaoRepository _repository;
    private readonly ILogger<NotificacaoDispatcher> _logger;
    private readonly ICanalResolver _canalResolver;

    public NotificacaoDispatcher(
        INotificacaoRepository repository,
        ILogger<NotificacaoDispatcher> logger,
        ICanalResolver canalResolver)
    {
        _repository = repository;
        _logger = logger;
        _canalResolver = canalResolver;
    }

    public async Task EnviarNotificacaoAsync(string notificacaoId)
    {
        var notificacao = await _repository.ObterPorIdAsync(notificacaoId)
            ?? throw new Exception($"Notificação {notificacaoId} não encontrada.");

        notificacao.Status = NotificacaoStatus.Processando;
        await _repository.AtualizarAsync(notificacao);

        try
        {
            _logger.LogInformation("Processando notificação {Id} com canal '{Canal}'", notificacao.Id, notificacao.Canal);

            var strategy = _canalResolver.Resolver(notificacao.Canal);


            if (strategy is null)
            {
                _logger.LogWarning("Nenhuma estratégia registrada para o canal: {Canal}", notificacao.Canal);
                throw new NotSupportedException($"Canal '{notificacao.Canal}' não suportado.");
            }


            await strategy.EnviarAsync(notificacao.Destinatario, notificacao.Mensagem, notificacao.Assunto);

            notificacao.MarcarComoEnviada();
            await _repository.AtualizarAsync(notificacao);

            _logger.LogInformation("Notificação {Id} enviada com sucesso", notificacao.Id);
        }
        catch (Exception ex)
        {
            notificacao.IncrementarTentativa();
            notificacao.MarcarComoFalha(ex.Message);
            await _repository.AtualizarAsync(notificacao);

            _logger.LogError(ex, "Falha no envio da notificação {Id}", notificacao.Id);
            throw;
        }
    }
}