﻿## Build results
bin/
obj/
[Ll]og/
*.log

## User-specific files
*.user
*.suo
*.userosscache
*.sln.docstates

## VS Code
.vscode/

## Visual Studio settings
.vs/
.vscode/
*.vssscc
*.vspscc
*.pdb

## Dependency directories
packages/
node_modules/

## Rider
.idea/

## ASP.NET Scaffolding
ScaffoldingReadMe.txt

## .NET Core
project.lock.json
project.fragment.lock.json
artifacts/
*.db
*.sqlite

## Auto-generated files
*.Generated.cs
*.g.cs
*.g.i.cs
*.AssemblyInfo.cs

## Publish output
wwwroot/dist/
wwwroot/build/
publish/
out/
dist/

## NuGet
*.nupkg
*.snupkg
.nuget/
*.nuspec
*.psmdcp
*.vsix

## Test results
TestResults/
*.trx
*.coverage
*.coveragexml
*.vspx
*.vsp

## Azure deployment
*.azurePubxml
*.Publish.xml
*.pubxml
*.publishproj

## Logs
*.log
logs/

## Others
*.DS_Store
*.swp
*.swo
*.bak
*.tmp

# Secret configs (if used)
appsettings.Local.json
appsettings.Development.json
secrets.json
.env

# Docker
docker-compose.override.yml
docker-compose.*.yml
**/Dockerfile.*
**/docker-compose.*.override.yml

# EF Core Migrations snapshot (optional)
**/Migrations/*Snapshot.cs

# JetBrains Rider (if you use)
.idea/
