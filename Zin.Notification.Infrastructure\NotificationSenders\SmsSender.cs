﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Retry;
using Zin.Notification.Application.Configs;
using Zin.Notification.Application.Interfaces;

namespace Zin.Notification.Infrastructure.ExternalClients;

public class SmsSender : ISmsSender
{
    private readonly SmsSettings _settings;
    private readonly ILogger<SmsSender> _logger;
    private readonly ResiliencePipeline _resilience;

    public SmsSender(IOptions<SmsSettings> settings, ILogger<SmsSender> logger)
    {
        _settings = settings.Value;
        _logger = logger;

        _resilience = new ResiliencePipelineBuilder()
            .AddRetry(new RetryStrategyOptions
            {
                ShouldHandle = new PredicateBuilder().Handle<Exception>(),
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromSeconds(1),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                OnRetry = args =>
                {
                    _logger.LogWarning("Tentativa {AttemptNumber} de envio de SMS falhou. Erro: {Exception}",
                        args.AttemptNumber, args.Outcome.Exception?.Message);
                    return ValueTask.CompletedTask;
                }
            })
            .AddTimeout(TimeSpan.FromSeconds(30))
            .Build();
    }

    public async Task<bool> EnviarAsync(string to, string body, string title)
    {
        var result = await _resilience.ExecuteAsync(_ =>
        {
            Console.WriteLine($"[MOCK SMS] To: {to}, From: {_settings.FromNumber}, Title: {title}, Body: {body}");
            return ValueTask.FromResult(true); // Sucesso
        });

        if(result)
            _logger.LogInformation("SMS enviado com sucesso para {Destinatario}", to);

        return result;
    }
}