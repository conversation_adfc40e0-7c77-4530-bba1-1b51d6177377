using Hangfire;
using Serilog;
using Zin.Notification.Api.Middlewares;
using Zin.Notification.Infrastructure.Configuration;
using Zin.Notification.Api.Authorization;

var builder = WebApplication.CreateBuilder(args);

//Configure Serilog
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

var corsPolicyName = "AllowAllOrigins";

builder.Services.AddCors(options =>
{
    options.AddPolicy(corsPolicyName, policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

//Configure Hangfire with MongoDB and Notification Services
builder.Services.AddHangfireWithMongo(builder.Configuration, "Zin.Notification.Api");
builder.Services.AddNotificationServices(builder.Configuration);

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseHangfireDashboard("/hangfire", new DashboardOptions
    {
        Authorization = new [] { new AllowAnonymousHangfireAuthorizationFilter() }
    });
}

app.UseSerilogRequestLogging();
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseCors(corsPolicyName);
app.UseAuthorization();
app.MapControllers();

app.Run();