﻿using System.Diagnostics;
using System.Text;

namespace Zin.Notification.Api.Middlewares;

public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var request = context.Request;

        var requestId = Guid.NewGuid().ToString();
        var path = request.Path;
        var method = request.Method;

        _logger.LogInformation("[{RequestId}] Início {Method} {Path}", requestId, method, path);

        if (method is "POST" or "PUT" or "PATCH")
        {
            request.EnableBuffering();

            using var reader = new StreamReader(request.Body, Encoding.UTF8, detectEncodingFromByteOrderMarks: false, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            request.Body.Position = 0;

            if (!string.IsNullOrWhiteSpace(body))
                _logger.LogInformation("[{RequestId}] Body: {Body}", requestId, body);
        }

        try
        {
            await _next(context);
            stopwatch.Stop();

            _logger.LogInformation("[{RequestId}] Fim {Method} {Path} => {StatusCode} em {Elapsed}ms",
                requestId, method, path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "[{RequestId}] Erro em {Method} {Path} em {Elapsed}ms",
                requestId, method, path, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }
}