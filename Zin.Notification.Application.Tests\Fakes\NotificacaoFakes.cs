using Zin.Notification.Domain.Entities;
using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Application.Tests.Fakes;

public static class NotificacaoFakes
{
    public static Notificacao CriarNotificacao(NotificacaoCanal canal, NotificacaoStatus status = NotificacaoStatus.Pendente)
    {
        return new Notificacao
        {
            Id = Guid.NewGuid().ToString(),
            Canal = canal,
            Destinatario = "<EMAIL>",
            Assunto = "Teste Título Notificação",
            Mensagem = "Teste corpo notificação.",
            Status = status,
            DataCriacao = DateTime.UtcNow
        };
    }

    public static Notificacao Clone(Notificacao original)
    {
        return new Notificacao
        {
            Id = original.Id,
            Canal = original.Canal,
            Destinatario = original.Destinatario,
            Assunto = original.Assunto,
            Mensagem = original.Mensagem,
            Status = original.Status,
            TentativasRealizadas = original.TentativasRealizadas,
            MaxTentativas = original.MaxTentativas,
            DataCriacao = original.DataCriacao,
            DataUltimaAtualização = original.DataUltimaAtualização,
            DataEnvio = original.DataEnvio,
            MensagemErro = original.MensagemErro,
            HistoricoTentativas = new List<TentativaEnvio>(original.HistoricoTentativas)
        };
    }
}
