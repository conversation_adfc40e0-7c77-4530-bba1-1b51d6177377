using Microsoft.Extensions.Logging;
using Zin.Notification.Application.Interfaces;

namespace Zin.Notification.Application.Strategies;
public class SmsStrategy : INotificacaoStrategy
{
    private readonly ISmsSender _smsSender;
    private readonly ILogger<SmsStrategy> _logger;

    public SmsStrategy(ISmsSender smsSender, ILogger<SmsStrategy> logger)
    {
        _smsSender = smsSender;
        _logger = logger;
    }

    public async Task<bool> EnviarAsync(string destinatario, string mensagem, string subject)
    {
        _logger.LogInformation("Enviando SMS via SmsStrategy para {Destinatario}", destinatario);
        var success = await _smsSender.EnviarAsync(destinatario, mensagem, subject);
        if (!success)
        {
            throw new Exception("Falha no envio do SMS.");
        }
        return true;
    }
}