﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Application.Resolvers;
using Zin.Notification.Application.Services;
using Zin.Notification.Application.Strategies;
using Zin.Notification.Domain.Entities;
using Zin.Notification.Domain.Enums;
using Zin.Notification.Infrastructure.ExternalClients;
using Zin.Notification.Infrastructure.Repositories;

namespace Zin.Notification.Infrastructure.Configuration;

public static class NotificacaoServiceConfiguration
{
    public static IServiceCollection AddNotificationServices(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("MongoDb")
            ?? throw new InvalidOperationException("MongoDb connection string is required");

        var mongoUrl = new MongoUrl(connectionString);
        var mongoClient = new MongoClient(mongoUrl);
        var database = mongoClient.GetDatabase(mongoUrl.DatabaseName);

        services.AddSingleton<IMongoClient>(mongoClient);
        services.AddSingleton<IMongoDatabase>(database);

        services.AddScoped<ICanalResolver, CanalResolver>();

        services.AddScoped<ISmsSender, SmsSender>();
        services.AddScoped<IEmailSender, EmailSender>();

        services.AddScoped<INotificacaoRepository, NotificacaoRepository>();

        services.AddScoped<INotificacaoOrchestrator, NotificacaoOrchestrator>();
        services.AddScoped<INotificacaoDispatcher, NotificacaoDispatcher>();

        services.AddKeyedScoped<INotificacaoStrategy, EmailStrategy>(NotificacaoCanal.Email);
        services.AddKeyedScoped<INotificacaoStrategy, SmsStrategy>(NotificacaoCanal.Sms);

        return services;
    }
}