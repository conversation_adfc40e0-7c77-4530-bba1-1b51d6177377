﻿using Microsoft.Extensions.DependencyInjection;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Application.Resolvers
{
    public class CanalResolver(IServiceProvider serviceProvider) : ICanalResolver
    {
        private readonly IServiceProvider _serviceProvider = serviceProvider;

        public INotificacaoStrategy Resolver(NotificacaoCanal canal)
        {
            var strategy = _serviceProvider.GetKeyedService<INotificacaoStrategy>(canal);

            if (strategy is null)
                throw new NotSupportedException($"Canal '{canal}' não suportado.");

            return strategy;
        }
    }
}