using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Application.Strategies;

namespace Zin.Notification.Application.Tests;

public class SmsStrategyTests
{
    private readonly Mock<ISmsSender> _mockSender;
    private readonly Mock<ILogger<SmsStrategy>> _mockLogger;
    private readonly INotificacaoStrategy _strategy;

    public SmsStrategyTests()
    {
        _mockSender = new Mock<ISmsSender>();
        _mockLogger = new Mock<ILogger<SmsStrategy>>();
        _strategy = new SmsStrategy(_mockSender.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task EnviarAsync_DeveLogarInformacaoDeSucesso_QuandoExecutado()
    {
        // Arrange
        var recipient = "+5511999999999";
        var message = "Test message";
        var subject = "Test";

        _mockSender.Setup(s => s.EnviarAsync(recipient, message, subject))
            .Returns(Task.FromResult(true));

        // Act
        await _strategy.EnviarAsync(recipient, message, subject);

        // Assert
        _mockSender.Verify(s => s.EnviarAsync(recipient, message, subject), Times.Once);

        _mockLogger.Verify(x => x.Log(
            LogLevel.Information,It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains($"Enviando SMS via SmsStrategy para {recipient}")),
            It.IsAny<Exception?>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}