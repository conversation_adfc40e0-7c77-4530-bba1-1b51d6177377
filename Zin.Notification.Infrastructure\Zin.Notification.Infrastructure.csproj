﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Zin.Notification.Application\Zin.Notification.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
    <PackageReference Include="Hangfire.Mongo" Version="1.11.6" />
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="Polly" Version="8.5.0" />
    <PackageReference Include="Polly.Extensions" Version="8.5.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
