version: '3.8'

services:
  mongo:
    image: mongo:latest
    container_name: zin-mongo
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh --quiet
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  seq:
    image: datalust/seq:latest
    container_name: zin-seq
    ports:
      - "5341:80"
    environment:
      - ACCEPT_EULA=Y
      - SEQ_FIRSTRUN_NOAUTHENTICATION=true
    volumes:
      - seq-data:/data

  notification-api:
    container_name: zin-notification-api
    build:
      context: .
      dockerfile: Zin.Notification.Api/Dockerfile
    ports:
      - "8080:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__MongoDb=mongodb://mongo:27017/ZinNotification
    depends_on:
      - mongo
      - seq

  notification-worker:
    container_name: zin-notification-worker
    build:
      context: .
      dockerfile: Zin.Notification.Worker/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__MongoDb=mongodb://mongo:27017/ZinNotification
      - Serilog__WriteTo__2__Args__serverUrl=http://seq:80
    depends_on:
      - mongo
      - seq

volumes:
  mongo-data:
    driver: local
  seq-data:
    driver: local