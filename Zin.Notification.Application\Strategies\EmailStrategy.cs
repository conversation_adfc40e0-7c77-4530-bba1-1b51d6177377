﻿using Microsoft.Extensions.Logging;
using Zin.Notification.Application.Interfaces;

namespace Zin.Notification.Application.Strategies;

public class EmailStrategy : INotificacaoStrategy
{
    private readonly IEmailSender _emailSender;
    private readonly ILogger<EmailStrategy> _logger;

    public EmailStrategy(IEmailSender emailSender, ILogger<EmailStrategy> logger)
    {
        _emailSender = emailSender;
        _logger = logger;
    }

    public async Task<bool> EnviarAsync(string destinatario, string mensagem, string? assunto)
    {
        _logger.LogInformation("Enviando e-mail via EmailStrategy para {Destinatario}", destinatario);

        var assuntoFinal = assunto ?? "Serviço de Notificação";

        var success = await _emailSender.EnviarAsync(destinatario, mensagem, assuntoFinal);
        if (!success)
        {
            throw new Exception("Falha no envio do e-mail.");
        }

        return true;
    }
}