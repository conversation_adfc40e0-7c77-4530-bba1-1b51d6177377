#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj and restore as distinct layers
COPY ["Zin.Notification.Api/Zin.Notification.Api.csproj", "Zin.Notification.Api/"]
COPY ["Zin.Notification.Domain/Zin.Notification.Domain.csproj", "Zin.Notification.Domain/"]
COPY ["Zin.Notification.Application/Zin.Notification.Application.csproj", "Zin.Notification.Application/"]
COPY ["Zin.Notification.Infrastructure/Zin.Notification.Infrastructure.csproj", "Zin.Notification.Infrastructure/"]
RUN dotnet restore "Zin.Notification.Api/Zin.Notification.Api.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src/Zin.Notification.Api"
RUN dotnet build "Zin.Notification.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Zin.Notification.Api.csproj" -c Release -o /app/publish

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Zin.Notification.Api.dll"]
