using Hangfire;
using Microsoft.Extensions.Logging;
using Zin.Notification.Application.Dtos;
using Zin.Notification.Application.Interfaces;
using Zin.Notification.Domain.Entities;
using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Application.Services;

public class NotificacaoOrchestrator : INotificacaoOrchestrator
{
    private readonly INotificacaoRepository _repository;
    private readonly ILogger<NotificacaoOrchestrator> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;

    public NotificacaoOrchestrator(
        INotificacaoRepository repository,
        ILogger<NotificacaoOrchestrator> logger,
        IBackgroundJobClient backgroundJobClient)
    {
        _repository = repository;
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
    }

    public async Task<string> CriarEEnfileirarAsync(NotificacaoRequest request)
    {
        var notificacao = new Notificacao
        {
            Id = Guid.NewGuid().ToString(),
            Canal = request.Channel,
            Destinatario = request.Recipient,
            Assunto = request.Subject,
            Mensagem = request.Message,
            Status = NotificacaoStatus.Pendente,
            DataCriacao = DateTime.UtcNow
        };

        await _repository.CriarAsync(notificacao);

        _backgroundJobClient.Enqueue<INotificacaoDispatcher>(svc =>
            svc.EnviarNotificacaoAsync(notificacao.Id));

        return notificacao.Id;
    }

    public async Task ReprocessarAsync(string notificacaoId)
    {
        _logger.LogInformation("Reprocessando notificação {Id}", notificacaoId);

        var notificacao = await _repository.ObterPorIdAsync(notificacaoId);
        if (notificacao == null)
            throw new ArgumentException($"Notificação {notificacaoId} não encontrada.");

        if (!notificacao.PodeReprocessar)
            throw new InvalidOperationException($"Notificação {notificacaoId} não pode ser reprocessada.");

        notificacao.ResetarParaReprocessamento();
        await _repository.AtualizarAsync(notificacao);

        _backgroundJobClient.Enqueue<INotificacaoDispatcher>(svc =>
            svc.EnviarNotificacaoAsync(notificacaoId));
    }

    public async Task<IEnumerable<Notificacao>> ObterFalhasAsync()
    {
        return await _repository.ObterFalhasParaReprocessamentoAsync();
    }

    public async Task<Notificacao?> ObterPorIdAsync(string id)
    {
        return await _repository.ObterPorIdAsync(id);
    }   
}