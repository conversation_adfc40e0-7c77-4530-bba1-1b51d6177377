{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"MongoDb": "mongodb://localhost:27017/ZinNotification"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Debug"}, {"Name": "Seq", "serverUrl": "http://localhost:5341"}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"], "Properties": {"Application": "Zin.Notification.Api"}}}