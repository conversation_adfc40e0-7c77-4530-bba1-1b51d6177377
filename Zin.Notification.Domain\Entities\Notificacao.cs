using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Domain.Entities;

public class Notificacao
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public NotificacaoCanal Canal { get; set; }
    public string Destinatario { get; set; } = string.Empty;
    public string Assunto {get; set; } = string.Empty;
    public string Mensagem { get; set; } = string.Empty;
    public NotificacaoStatus Status { get; set; } = NotificacaoStatus.Pendente;
    public int TentativasRealizadas { get; set; } = 0;
    public int MaxTentativas { get; set; } = 3;
    public DateTime DataCriacao { get; set; } = DateTime.UtcNow;
    public DateTime? DataUltimaAtualização { get; set; }
    public DateTime? DataEnvio { get; set; }
    public string? MensagemErro { get; set; }
    public List<TentativaEnvio> HistoricoTentativas { get; set; } = new();

    public bool PodeReprocessar => Status == NotificacaoStatus.Falha;
    public bool PodeTentarNovamente => TentativasRealizadas < MaxTentativas && Status != NotificacaoStatus.Enviada;

    public void MarcarComoEnviada()
    {
        Status = NotificacaoStatus.Enviada;
        DataEnvio = DateTime.UtcNow;
        DataUltimaAtualização = DateTime.UtcNow;
    }

    public void MarcarComoFalha(string mensagemErro)
    {
        Status = NotificacaoStatus.Falha;
        MensagemErro = mensagemErro;
        DataUltimaAtualização = DateTime.UtcNow;
    }

    public void IncrementarTentativa(string? erro = null)
    {
        TentativasRealizadas++;
        DataUltimaAtualização = DateTime.UtcNow;

        HistoricoTentativas.Add(new TentativaEnvio(
            TentativasRealizadas,
            DateTime.UtcNow,
            string.IsNullOrEmpty(erro),
            erro
        ));

        if (TentativasRealizadas >= MaxTentativas && !string.IsNullOrEmpty(erro))
        {
            MarcarComoFalha(erro);
        }
    }

    public void ResetarParaReprocessamento()
    {
        if (!PodeReprocessar) return;

        Status = NotificacaoStatus.Pendente;
        TentativasRealizadas = 0;
        MensagemErro = null;
        DataUltimaAtualização = DateTime.UtcNow;
    }
}

public record TentativaEnvio(
    int NumeroTentativa,
    DateTime DataTentativa,
    bool Sucesso,
    string? MensagemErro
);