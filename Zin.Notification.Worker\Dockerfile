FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY . .
RUN dotnet clean "Zin.Notification.Worker/Zin.Notification.Worker.csproj" && dotnet nuget locals all --clear && dotnet restore "Zin.Notification.Worker/Zin.Notification.Worker.csproj"

WORKDIR "/src/Zin.Notification.Worker"
RUN dotnet build "Zin.Notification.Worker.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Zin.Notification.Worker.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
COPY Zin.Notification.Worker/appsettings.json .
ENTRYPOINT ["dotnet", "Zin.Notification.Worker.dll"]