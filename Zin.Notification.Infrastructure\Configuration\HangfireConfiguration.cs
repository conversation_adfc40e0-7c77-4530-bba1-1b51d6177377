﻿using Hangfire;
using Hangfire.Mongo;
using Hangfire.Mongo.Migration.Strategies;
using Hangfire.Mongo.Migration.Strategies.Backup;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;

namespace Zin.Notification.Infrastructure.Configuration;

public static class HangfireConfiguration
{
    public static IServiceCollection AddHangfireWithMongo(
        this IServiceCollection services, 
        IConfiguration configuration,
        string serverName,
        string[]? queues = null)
    {
        var connectionString = configuration.GetConnectionString("MongoDb")
            ?? throw new InvalidOperationException("MongoDb connection string is required");

        var mongoUrlBuilder = new MongoUrlBuilder(connectionString);
        var mongoClient = new MongoClient(mongoUrlBuilder.ToMongoUrl());

        services.AddHangfire(config => 
        config
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseMongoStorage(mongoClient, mongoUrlBuilder.DatabaseName, new MongoStorageOptions
            {
                MigrationOptions = new MongoMigrationOptions
                {
                    MigrationStrategy = new MigrateMongoMigrationStrategy(),
                    BackupStrategy = new CollectionMongoBackupStrategy()
                },
                Prefix = "hangfire.mongo",
                CheckQueuedJobsStrategy = CheckQueuedJobsStrategy.TailNotificationsCollection
            }));

        return services;
    }
}