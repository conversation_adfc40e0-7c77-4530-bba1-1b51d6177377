﻿using MongoDB.Driver;
using Zin.Notification.Domain.Entities;
using Zin.Notification.Domain.Enums;

namespace Zin.Notification.Infrastructure.Repositories;

public class NotificacaoRepository : INotificacaoRepository
{
    private readonly IMongoCollection<Notificacao> _collection;

    public NotificacaoRepository(IMongoDatabase database)
    {
        _collection = database.GetCollection<Notificacao>("notificacoes");
        
        // Criar índices para performance
        var indexKeysDefinition = Builders<Notificacao>.IndexKeys
            .Ascending(x => x.Status)
            .Ascending(x => x.DataCriacao);
        
        _collection.Indexes.CreateOneAsync(new CreateIndexModel<Notificacao>(indexKeysDefinition));
    }

    public async Task<string> CriarAsync(Notificacao notificacao)
    {
        await _collection.InsertOneAsync(notificacao);
        return notificacao.Id;
    }

    public async Task<Notificacao?> ObterPorIdAsync(string id)
    {
        return await _collection.Find(x => x.Id == id).FirstOrDefaultAsync();
    }

    public async Task AtualizarAsync(Notificacao notificacao)
    {
        await _collection.ReplaceOneAsync(x => x.Id == notificacao.Id, notificacao);
    }

    public async Task<IEnumerable<Notificacao>> ObterPorStatusAsync(NotificacaoStatus status)
    {
        return await _collection.Find(x => x.Status == status).ToListAsync();
    }

    public async Task<IEnumerable<Notificacao>> ObterFalhasParaReprocessamentoAsync()
    {
        return await _collection.Find(x => x.Status == NotificacaoStatus.Falha).ToListAsync();
    }

    public async Task<IEnumerable<Notificacao>> ObterPendentesAsync()
    {
        return await _collection.Find(x => x.Status == NotificacaoStatus.Pendente).ToListAsync();
    }
}
