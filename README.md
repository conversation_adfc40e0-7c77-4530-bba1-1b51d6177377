### Arquitetura e Fluxo da Notificação

O projeto `Zin.Notification` utiliza uma arquitetura limpa para separar as responsabilidades, resultando em um sistema robusto, testável e de fácil manutenção.

#### Camadas do Projeto

*   **`Zin.Notification.Domain`**: O núcleo do sistema. Contém as entidades de negócio (como `Notificacao`) e enums, sem dependências externas.
*   **`Zin.Notification.Application`**: Define o orquestrador (`INotificacaoOrchestrator`) e o dispatcher (`INotificacaoDispatcher`), além das interfaces de serviço (ex: `IEmailSender`), mantendo a lógica de orquestração e controle de fluxo.
*   **`Zin.Notification.Infrastructure`**: Implementa as interfaces definidas na camada de `Application`. É responsável por interagir com tecnologias externas, como o banco de dados (MongoDB), serviços de e-mail (MailKit) e SMS.
*   **`Zin.Notification.Api`**: A porta de entrada do sistema. Expõe endpoints para receber requisições de notificação. É responsável apenas por receber os dados e delegar o trabalho para a camada de `Application`.
*   **`Zin.Notification.Worker`**: Um processo em segundo plano (background service) responsável por processar as notificações enfileiradas, garantindo que a API possa responder rapidamente sem esperar o envio final.

#### Fluxo de uma Nova Notificação

1.  **Requisição**: Um cliente envia uma requisição `POST` para o endpoint `/api/Notificacao` no projeto `Zin.Notification.Api`.
2.  **Criação e Enfileiramento**:
    *   O `NotificacaoController` recebe a requisição.
    *   Ele chama o `NotificacaoOrchestrator` (camada de `Application`).
    *   O orquestrador cria uma nova entidade `Notificacao` com o status `Pendente` e a salva no MongoDB.
    *   Em seguida, ele enfileira um novo trabalho no **Hangfire**, instruindo o sistema a executar o `NotificacaoDispatcher` para aquela notificação específica. A configuração do Hangfire ocorre no Worker, com os métodos `Enqueue` sendo chamados na orquestração.
    *   A API retorna imediatamente uma resposta `202 Accepted` com o ID da notificação, sem esperar o envio.
3.  **Processamento em Background**:
    *   O projeto `Zin.Notification.Worker`, que está rodando em segundo plano, detecta o novo trabalho na fila do Hangfire.
    *   O `NotificacaoDispatcher` (camada de `Application`) é executado pelo Worker.
    *   Ele busca a notificação no banco de dados e atualiza seu status para `Processando`.
4.  **Seleção de Estratégia**:
    *   O `NotificacaoDispatcher` usa um `CanalResolver` para determinar a estratégia de envio correta (`EmailStrategy` ou `SmsStrategy`) com base no canal especificado na notificação.
5.  **Envio**:
    *   A estratégia escolhida (ex: `EmailStrategy`) chama o serviço correspondente na camada de `Infrastructure` (ex: `EmailSender`).
    *   O `EmailSender` utiliza a biblioteca MailKit para se conectar a um servidor SMTP e enviar o e-mail.
6.  **Finalização**:
    *   Se o envio for bem-sucedido, o `NotificacaoDispatcher` atualiza o status da notificação para `Enviada`.
    *   Se ocorrer um erro, ele incrementa o número de tentativas e atualiza o status para `Falha`, armazenando a mensagem de erro. O Hangfire pode ser configurado para tentar novamente mais tarde.

### Como Executar o Projeto

Existem duas maneiras principais de executar o projeto: usando Docker (recomendado para um ambiente de produção e para garantir a consistência) ou diretamente pelo Visual Studio (ideal para desenvolvimento e depuração).

#### Opção 1: Executando com Docker (Recomendado)

O projeto é totalmente containerizado, o que simplifica a configuração.

**Pré-requisitos:**
*   Docker
*   Docker Compose

**Passos:**

1.  **Clone o Repositório**.
2.  **Inicie os Contêineres**: Execute o seguinte comando na raiz do projeto:
    ```bash
    docker-compose up -d --build
    ```
    Este comando irá construir e iniciar todos os serviços definidos no arquivo `docker-compose.yml`, incluindo a API, o Worker, o MongoDB e o Seq.

**Acessando os Serviços (Docker):**
*   **API (Swagger)**: `http://localhost:8080/swagger`
*   **Hangfire Dashboard**: `http://localhost:8080/hangfire`
*   **Seq (Logs)**: `http://localhost:5341`

---

#### Opção 2: Executando com Visual Studio

Esta abordagem permite depurar o código diretamente do seu ambiente de desenvolvimento.

**Pré-requisitos:**
*   .NET 9 SDK (ou a versão especificada nos arquivos `.csproj`)
*   Visual Studio 2022 ou superior
*   Uma instância do MongoDB e do Seq em execução. Você pode iniciá-las separadamente via Docker:
    ```bash
    docker-compose up -d mongo seq
    ```

**Passos:**

1.  **Abra a Solução**: Abra o arquivo `Zin.Notification.sln` no Visual Studio.
2.  **Configure Múltiplos Projetos de Inicialização**:
    *   Clique com o botão direito na Solução (`Solution 'Zin.Notification'`) no *Solution Explorer*.
    *   Selecione **`Configure Startup Projects...`**.
    *   Escolha a opção **`Multiple startup projects`**.
    *   Defina a **Ação (Action)** como **`Start`** para os seguintes projetos:
        *   `Zin.Notification.Api`
        *   `Zin.Notification.Worker`
3.  **Execute o Projeto**: Pressione **F5** ou clique no botão de "play" (Iniciar) do Visual Studio.

    O Visual Studio irá compilar e iniciar a API e o Worker simultaneamente. O Worker é um aplicativo de console que ficará em execução, processando os trabalhos em segundo plano, enquanto a API estará pronta para receber requisições.

**Acessando os Serviços (Visual Studio):**

Ao executar pelo Visual Studio, as portas são definidas no arquivo `launchSettings.json`.

*   **API (Swagger)**: `http://localhost:5118/swagger` ou `https://localhost:7284/swagger`
*   **Hangfire Dashboard**: `http://localhost:5118/hangfire` ou `https://localhost:7284/hangfire`

### Configuração de Credenciais (Importante)

Atualmente, as credenciais estão sendo definidas diretamente no `appsettings.json` do projeto `Zin.Notification.Worker`, o que facilita o desenvolvimento local, mas não é a abordagem mais recomendada, principalmente em ambientes versionados (ex: Git).

**Onde configurar:**

*   **Localmente (Visual Studio):**

    No `appsettings.json` na raiz do projeto `Zin.Notification.Worker` existem as seguintes configurações:

    ```json
    {
      "EmailSettings": {
        "User": "<EMAIL>",
        "Password": "sua-senha"
      },
      "SmsSettings": {
        "AccountSid": "seu_sid",
        "AuthToken": "seu_token"
      }
    }
    ```

**Credenciais a serem configuradas:**

*   **`EmailSettings`**: 
    *   `User`: O nome de usuário para o servidor SMTP.
    *   `Password`: A senha para o servidor SMTP.
*   **`SmsSettings`**: 
    *   `AccountSid`: O SID da sua conta Twilio (ou outro provedor).
    *   `AuthToken`: O token de autenticação do seu provedor de SMS.
