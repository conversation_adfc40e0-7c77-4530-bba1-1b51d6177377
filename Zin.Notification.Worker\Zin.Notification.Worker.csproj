﻿<Project Sdk="Microsoft.NET.Sdk">

	<ItemGroup>
		<ProjectReference Include="..\Zin.Notification.Application\Zin.Notification.Application.csproj" />
		<ProjectReference Include="..\Zin.Notification.Infrastructure\Zin.Notification.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="6.0.1" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
		<PackageReference Include="MongoDB.Bson" Version="3.4.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
	</ItemGroup>

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>	
  </PropertyGroup>

	<ItemGroup>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>
